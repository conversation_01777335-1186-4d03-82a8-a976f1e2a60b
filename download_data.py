from bing_image_downloader import downloader
import os

def download_person_images():
    """
    使用 bing-image-downloader 下载包含人物的图片。
    """
    # --- 配置参数 ---
    
    # 定义搜索关键词。使用多个关键词可以获取更多样化的图片。
    # 你可以修改或添加更多关键词。
    search_queries = [
        "person walking on street", # 街上行走的人
        "people at a cafe",         # 咖啡馆里的人
        "family portrait",          #家庭合照
        "students in a classroom",  # 教室里的学生
        "person standing still"     # 站着的人
    ]
    
    # 每个关键词下载的图片数量
    # 总数大约是 20 * 5 = 100 张
    images_per_query = 20
    
    # 图片保存的总目录
    output_dir = "person_images"
    
    print(f"开始下载图片，将保存在 '{output_dir}' 文件夹中...")
    
    # --- 开始下载 ---
    for query in search_queries:
        print("-" * 40)
        print(f"正在下载关键词: '{query}'")
        
        try:
            downloader.download(
                query,
                limit=images_per_query,
                output_dir=output_dir,
                adult_filter_off=True,
                force_replace=False,
                timeout=60,
                verbose=True  # 设置为 True 来查看详细的下载过程
            )
        except Exception as e:
            print(f"下载 '{query}' 时出错: {e}")
            
    print("-" * 40)
    print("🎉 所有下载任务已完成！")
    print(f"图片已保存到: {os.path.abspath(output_dir)}")

if __name__ == '__main__':
    download_person_images()
