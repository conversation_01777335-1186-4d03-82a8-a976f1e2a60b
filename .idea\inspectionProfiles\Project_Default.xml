<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="295">
            <item index="0" class="java.lang.String" itemvalue="natsort" />
            <item index="1" class="java.lang.String" itemvalue="opencv-python" />
            <item index="2" class="java.lang.String" itemvalue="torch" />
            <item index="3" class="java.lang.String" itemvalue="torchvision" />
            <item index="4" class="java.lang.String" itemvalue="scikit-image" />
            <item index="5" class="java.lang.String" itemvalue="facexlib" />
            <item index="6" class="java.lang.String" itemvalue="gradio" />
            <item index="7" class="java.lang.String" itemvalue="open-clip-torch" />
            <item index="8" class="java.lang.String" itemvalue="torchdiffeq" />
            <item index="9" class="java.lang.String" itemvalue="clean-fid" />
            <item index="10" class="java.lang.String" itemvalue="resize-right" />
            <item index="11" class="java.lang.String" itemvalue="kornia" />
            <item index="12" class="java.lang.String" itemvalue="pillow-avif-plugin" />
            <item index="13" class="java.lang.String" itemvalue="accelerate" />
            <item index="14" class="java.lang.String" itemvalue="lark" />
            <item index="15" class="java.lang.String" itemvalue="piexif" />
            <item index="16" class="java.lang.String" itemvalue="GitPython" />
            <item index="17" class="java.lang.String" itemvalue="transformers" />
            <item index="18" class="java.lang.String" itemvalue="jsonmerge" />
            <item index="19" class="java.lang.String" itemvalue="tomesd" />
            <item index="20" class="java.lang.String" itemvalue="blendmodes" />
            <item index="21" class="java.lang.String" itemvalue="pytorch_lightning" />
            <item index="22" class="java.lang.String" itemvalue="omegaconf" />
            <item index="23" class="java.lang.String" itemvalue="psutil" />
            <item index="24" class="java.lang.String" itemvalue="torchsde" />
            <item index="25" class="java.lang.String" itemvalue="fastapi" />
            <item index="26" class="java.lang.String" itemvalue="einops" />
            <item index="27" class="java.lang.String" itemvalue="diskcache" />
            <item index="28" class="java.lang.String" itemvalue="inflection" />
            <item index="29" class="java.lang.String" itemvalue="albumentations" />
            <item index="30" class="java.lang.String" itemvalue="streamlit-drawable-canvas" />
            <item index="31" class="java.lang.String" itemvalue="pytorch-lightning" />
            <item index="32" class="java.lang.String" itemvalue="torchmetrics" />
            <item index="33" class="java.lang.String" itemvalue="imageio" />
            <item index="34" class="java.lang.String" itemvalue="pudb" />
            <item index="35" class="java.lang.String" itemvalue="test-tube" />
            <item index="36" class="java.lang.String" itemvalue="streamlit" />
            <item index="37" class="java.lang.String" itemvalue="imageio-ffmpeg" />
            <item index="38" class="java.lang.String" itemvalue="webdataset" />
            <item index="39" class="java.lang.String" itemvalue="invisible-watermark" />
            <item index="40" class="java.lang.String" itemvalue="tzlocal" />
            <item index="41" class="java.lang.String" itemvalue="lvis" />
            <item index="42" class="java.lang.String" itemvalue="validators" />
            <item index="43" class="java.lang.String" itemvalue="python-slugify" />
            <item index="44" class="java.lang.String" itemvalue="srsly" />
            <item index="45" class="java.lang.String" itemvalue="gitdb" />
            <item index="46" class="java.lang.String" itemvalue="markdown-it-py" />
            <item index="47" class="java.lang.String" itemvalue="python-magic" />
            <item index="48" class="java.lang.String" itemvalue="ftfy" />
            <item index="49" class="java.lang.String" itemvalue="ipython-genutils" />
            <item index="50" class="java.lang.String" itemvalue="PyNaCl" />
            <item index="51" class="java.lang.String" itemvalue="bleach" />
            <item index="52" class="java.lang.String" itemvalue="jupyter_server_terminals" />
            <item index="53" class="java.lang.String" itemvalue="lxml" />
            <item index="54" class="java.lang.String" itemvalue="soupsieve" />
            <item index="55" class="java.lang.String" itemvalue="jsonschema" />
            <item index="56" class="java.lang.String" itemvalue="nbclassic" />
            <item index="57" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="58" class="java.lang.String" itemvalue="tensorboard-data-server" />
            <item index="59" class="java.lang.String" itemvalue="click" />
            <item index="60" class="java.lang.String" itemvalue="contourpy" />
            <item index="61" class="java.lang.String" itemvalue="regex" />
            <item index="62" class="java.lang.String" itemvalue="tensorboard" />
            <item index="63" class="java.lang.String" itemvalue="cymem" />
            <item index="64" class="java.lang.String" itemvalue="matplotlib" />
            <item index="65" class="java.lang.String" itemvalue="murmurhash" />
            <item index="66" class="java.lang.String" itemvalue="idna" />
            <item index="67" class="java.lang.String" itemvalue="rsa" />
            <item index="68" class="java.lang.String" itemvalue="wasabi" />
            <item index="69" class="java.lang.String" itemvalue="confection" />
            <item index="70" class="java.lang.String" itemvalue="smmap" />
            <item index="71" class="java.lang.String" itemvalue="pkgutil_resolve_name" />
            <item index="72" class="java.lang.String" itemvalue="datasets" />
            <item index="73" class="java.lang.String" itemvalue="jupyter-events" />
            <item index="74" class="java.lang.String" itemvalue="nvidia-nccl-cu12" />
            <item index="75" class="java.lang.String" itemvalue="opencv-python-headless" />
            <item index="76" class="java.lang.String" itemvalue="exceptiongroup" />
            <item index="77" class="java.lang.String" itemvalue="pycocoevalcap" />
            <item index="78" class="java.lang.String" itemvalue="gdown" />
            <item index="79" class="java.lang.String" itemvalue="jupyter" />
            <item index="80" class="java.lang.String" itemvalue="PyWavelets" />
            <item index="81" class="java.lang.String" itemvalue="mdurl" />
            <item index="82" class="java.lang.String" itemvalue="smart-open" />
            <item index="83" class="java.lang.String" itemvalue="opendatasets" />
            <item index="84" class="java.lang.String" itemvalue="black" />
            <item index="85" class="java.lang.String" itemvalue="jsonpointer" />
            <item index="86" class="java.lang.String" itemvalue="Send2Trash" />
            <item index="87" class="java.lang.String" itemvalue="nvidia-cudnn-cu12" />
            <item index="88" class="java.lang.String" itemvalue="pytz-deprecation-shim" />
            <item index="89" class="java.lang.String" itemvalue="ultralytics" />
            <item index="90" class="java.lang.String" itemvalue="docker-pycreds" />
            <item index="91" class="java.lang.String" itemvalue="magic-wormhole" />
            <item index="92" class="java.lang.String" itemvalue="importlib-resources" />
            <item index="93" class="java.lang.String" itemvalue="pathtools" />
            <item index="94" class="java.lang.String" itemvalue="autobahn" />
            <item index="95" class="java.lang.String" itemvalue="watchdog" />
            <item index="96" class="java.lang.String" itemvalue="debugpy" />
            <item index="97" class="java.lang.String" itemvalue="txtorcon" />
            <item index="98" class="java.lang.String" itemvalue="argon2-cffi" />
            <item index="99" class="java.lang.String" itemvalue="multidict" />
            <item index="100" class="java.lang.String" itemvalue="thinc" />
            <item index="101" class="java.lang.String" itemvalue="pytz" />
            <item index="102" class="java.lang.String" itemvalue="absl-py" />
            <item index="103" class="java.lang.String" itemvalue="protobuf" />
            <item index="104" class="java.lang.String" itemvalue="shapely" />
            <item index="105" class="java.lang.String" itemvalue="joblib" />
            <item index="106" class="java.lang.String" itemvalue="nltk" />
            <item index="107" class="java.lang.String" itemvalue="tinycss2" />
            <item index="108" class="java.lang.String" itemvalue="fsspec" />
            <item index="109" class="java.lang.String" itemvalue="nvidia-curand-cu12" />
            <item index="110" class="java.lang.String" itemvalue="python-json-logger" />
            <item index="111" class="java.lang.String" itemvalue="docopt" />
            <item index="112" class="java.lang.String" itemvalue="lit" />
            <item index="113" class="java.lang.String" itemvalue="pyzmq" />
            <item index="114" class="java.lang.String" itemvalue="sentencepiece" />
            <item index="115" class="java.lang.String" itemvalue="oauthlib" />
            <item index="116" class="java.lang.String" itemvalue="pyparsing" />
            <item index="117" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="118" class="java.lang.String" itemvalue="hyperlink" />
            <item index="119" class="java.lang.String" itemvalue="service-identity" />
            <item index="120" class="java.lang.String" itemvalue="isoduration" />
            <item index="121" class="java.lang.String" itemvalue="nvidia-cuda-nvrtc-cu12" />
            <item index="122" class="java.lang.String" itemvalue="tifffile" />
            <item index="123" class="java.lang.String" itemvalue="fqdn" />
            <item index="124" class="java.lang.String" itemvalue="pathy" />
            <item index="125" class="java.lang.String" itemvalue="catalogue" />
            <item index="126" class="java.lang.String" itemvalue="altair" />
            <item index="127" class="java.lang.String" itemvalue="torch-fidelity" />
            <item index="128" class="java.lang.String" itemvalue="kaggle" />
            <item index="129" class="java.lang.String" itemvalue="urwid" />
            <item index="130" class="java.lang.String" itemvalue="ninja" />
            <item index="131" class="java.lang.String" itemvalue="widgetsnbextension" />
            <item index="132" class="java.lang.String" itemvalue="argon2-cffi-bindings" />
            <item index="133" class="java.lang.String" itemvalue="distlib" />
            <item index="134" class="java.lang.String" itemvalue="cfgv" />
            <item index="135" class="java.lang.String" itemvalue="pathspec" />
            <item index="136" class="java.lang.String" itemvalue="webcolors" />
            <item index="137" class="java.lang.String" itemvalue="mypy-extensions" />
            <item index="138" class="java.lang.String" itemvalue="spacy-legacy" />
            <item index="139" class="java.lang.String" itemvalue="braceexpand" />
            <item index="140" class="java.lang.String" itemvalue="Jinja2" />
            <item index="141" class="java.lang.String" itemvalue="rfc3986-validator" />
            <item index="142" class="java.lang.String" itemvalue="uri-template" />
            <item index="143" class="java.lang.String" itemvalue="fvcore" />
            <item index="144" class="java.lang.String" itemvalue="lightning-utilities" />
            <item index="145" class="java.lang.String" itemvalue="wandb" />
            <item index="146" class="java.lang.String" itemvalue="tomli" />
            <item index="147" class="java.lang.String" itemvalue="txaio" />
            <item index="148" class="java.lang.String" itemvalue="blis" />
            <item index="149" class="java.lang.String" itemvalue="timm" />
            <item index="150" class="java.lang.String" itemvalue="typer" />
            <item index="151" class="java.lang.String" itemvalue="identify" />
            <item index="152" class="java.lang.String" itemvalue="text-unidecode" />
            <item index="153" class="java.lang.String" itemvalue="ipython" />
            <item index="154" class="java.lang.String" itemvalue="rich" />
            <item index="155" class="java.lang.String" itemvalue="dill" />
            <item index="156" class="java.lang.String" itemvalue="packaging" />
            <item index="157" class="java.lang.String" itemvalue="fastjsonschema" />
            <item index="158" class="java.lang.String" itemvalue="Hydra" />
            <item index="159" class="java.lang.String" itemvalue="chardet" />
            <item index="160" class="java.lang.String" itemvalue="hkdf" />
            <item index="161" class="java.lang.String" itemvalue="aiohttp" />
            <item index="162" class="java.lang.String" itemvalue="pyDeprecate" />
            <item index="163" class="java.lang.String" itemvalue="tabulate" />
            <item index="164" class="java.lang.String" itemvalue="yarg" />
            <item index="165" class="java.lang.String" itemvalue="nvidia-cuda-cupti-cu12" />
            <item index="166" class="java.lang.String" itemvalue="nvidia-cufft-cu12" />
            <item index="167" class="java.lang.String" itemvalue="defusedxml" />
            <item index="168" class="java.lang.String" itemvalue="pyasn1-modules" />
            <item index="169" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="170" class="java.lang.String" itemvalue="multiprocess" />
            <item index="171" class="java.lang.String" itemvalue="cchardet" />
            <item index="172" class="java.lang.String" itemvalue="nvidia-cuda-runtime-cu12" />
            <item index="173" class="java.lang.String" itemvalue="sacrebleu" />
            <item index="174" class="java.lang.String" itemvalue="pyre-extensions" />
            <item index="175" class="java.lang.String" itemvalue="qtconsole" />
            <item index="176" class="java.lang.String" itemvalue="terminado" />
            <item index="177" class="java.lang.String" itemvalue="comm" />
            <item index="178" class="java.lang.String" itemvalue="portalocker" />
            <item index="179" class="java.lang.String" itemvalue="pydantic" />
            <item index="180" class="java.lang.String" itemvalue="jupyterlab-pygments" />
            <item index="181" class="java.lang.String" itemvalue="ipykernel" />
            <item index="182" class="java.lang.String" itemvalue="nbconvert" />
            <item index="183" class="java.lang.String" itemvalue="attrs" />
            <item index="184" class="java.lang.String" itemvalue="jupyter_server" />
            <item index="185" class="java.lang.String" itemvalue="nvidia-cublas-cu12" />
            <item index="186" class="java.lang.String" itemvalue="iopath" />
            <item index="187" class="java.lang.String" itemvalue="humanize" />
            <item index="188" class="java.lang.String" itemvalue="contexttimer" />
            <item index="189" class="java.lang.String" itemvalue="yacs" />
            <item index="190" class="java.lang.String" itemvalue="brotlipy" />
            <item index="191" class="java.lang.String" itemvalue="nvidia-nvjitlink-cu12" />
            <item index="192" class="java.lang.String" itemvalue="nvidia-cusparse-cu12" />
            <item index="193" class="java.lang.String" itemvalue="pandocfilters" />
            <item index="194" class="java.lang.String" itemvalue="antlr4-python3-runtime" />
            <item index="195" class="java.lang.String" itemvalue="pyasn1" />
            <item index="196" class="java.lang.String" itemvalue="bitarray" />
            <item index="197" class="java.lang.String" itemvalue="imgaug" />
            <item index="198" class="java.lang.String" itemvalue="sniffio" />
            <item index="199" class="java.lang.String" itemvalue="websocket-client" />
            <item index="200" class="java.lang.String" itemvalue="pyrsistent" />
            <item index="201" class="java.lang.String" itemvalue="mkl-fft" />
            <item index="202" class="java.lang.String" itemvalue="seaborn" />
            <item index="203" class="java.lang.String" itemvalue="zipp" />
            <item index="204" class="java.lang.String" itemvalue="tenacity" />
            <item index="205" class="java.lang.String" itemvalue="ipywidgets" />
            <item index="206" class="java.lang.String" itemvalue="blinker" />
            <item index="207" class="java.lang.String" itemvalue="pyarrow" />
            <item index="208" class="java.lang.String" itemvalue="lazy_loader" />
            <item index="209" class="java.lang.String" itemvalue="scipy" />
            <item index="210" class="java.lang.String" itemvalue="spake2" />
            <item index="211" class="java.lang.String" itemvalue="salesforce-lavis" />
            <item index="212" class="java.lang.String" itemvalue="tornado" />
            <item index="213" class="java.lang.String" itemvalue="google-auth-oauthlib" />
            <item index="214" class="java.lang.String" itemvalue="pytorch-triton" />
            <item index="215" class="java.lang.String" itemvalue="pytorch-fid" />
            <item index="216" class="java.lang.String" itemvalue="plotly" />
            <item index="217" class="java.lang.String" itemvalue="incremental" />
            <item index="218" class="java.lang.String" itemvalue="overrides" />
            <item index="219" class="java.lang.String" itemvalue="toml" />
            <item index="220" class="java.lang.String" itemvalue="langcodes" />
            <item index="221" class="java.lang.String" itemvalue="mistune" />
            <item index="222" class="java.lang.String" itemvalue="pandas" />
            <item index="223" class="java.lang.String" itemvalue="termcolor" />
            <item index="224" class="java.lang.String" itemvalue="hydra-core" />
            <item index="225" class="java.lang.String" itemvalue="toolz" />
            <item index="226" class="java.lang.String" itemvalue="future" />
            <item index="227" class="java.lang.String" itemvalue="cmake" />
            <item index="228" class="java.lang.String" itemvalue="jupyter-console" />
            <item index="229" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="230" class="java.lang.String" itemvalue="cachetools" />
            <item index="231" class="java.lang.String" itemvalue="responses" />
            <item index="232" class="java.lang.String" itemvalue="yarl" />
            <item index="233" class="java.lang.String" itemvalue="setproctitle" />
            <item index="234" class="java.lang.String" itemvalue="webencodings" />
            <item index="235" class="java.lang.String" itemvalue="Pillow" />
            <item index="236" class="java.lang.String" itemvalue="Twisted" />
            <item index="237" class="java.lang.String" itemvalue="hydra-submitit-launcher" />
            <item index="238" class="java.lang.String" itemvalue="notebook_shim" />
            <item index="239" class="java.lang.String" itemvalue="Automat" />
            <item index="240" class="java.lang.String" itemvalue="rfc3339-validator" />
            <item index="241" class="java.lang.String" itemvalue="arrow" />
            <item index="242" class="java.lang.String" itemvalue="huggingface-hub" />
            <item index="243" class="java.lang.String" itemvalue="xformers" />
            <item index="244" class="java.lang.String" itemvalue="pre-commit" />
            <item index="245" class="java.lang.String" itemvalue="nbclient" />
            <item index="246" class="java.lang.String" itemvalue="QtPy" />
            <item index="247" class="java.lang.String" itemvalue="cycler" />
            <item index="248" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="249" class="java.lang.String" itemvalue="constantly" />
            <item index="250" class="java.lang.String" itemvalue="frozenlist" />
            <item index="251" class="java.lang.String" itemvalue="submitit" />
            <item index="252" class="java.lang.String" itemvalue="nvidia-cusolver-cu12" />
            <item index="253" class="java.lang.String" itemvalue="spacy" />
            <item index="254" class="java.lang.String" itemvalue="appdirs" />
            <item index="255" class="java.lang.String" itemvalue="jupyterlab-widgets" />
            <item index="256" class="java.lang.String" itemvalue="backports.zoneinfo" />
            <item index="257" class="java.lang.String" itemvalue="safetensors" />
            <item index="258" class="java.lang.String" itemvalue="certifi" />
            <item index="259" class="java.lang.String" itemvalue="anyio" />
            <item index="260" class="java.lang.String" itemvalue="Markdown" />
            <item index="261" class="java.lang.String" itemvalue="sympy" />
            <item index="262" class="java.lang.String" itemvalue="notebook" />
            <item index="263" class="java.lang.String" itemvalue="xxhash" />
            <item index="264" class="java.lang.String" itemvalue="triton" />
            <item index="265" class="java.lang.String" itemvalue="nodeenv" />
            <item index="266" class="java.lang.String" itemvalue="jupyter_client" />
            <item index="267" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="268" class="java.lang.String" itemvalue="zope.interface" />
            <item index="269" class="java.lang.String" itemvalue="Pympler" />
            <item index="270" class="java.lang.String" itemvalue="fonttools" />
            <item index="271" class="java.lang.String" itemvalue="nvidia-nvtx-cu12" />
            <item index="272" class="java.lang.String" itemvalue="fairscale" />
            <item index="273" class="java.lang.String" itemvalue="virtualenv" />
            <item index="274" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="275" class="java.lang.String" itemvalue="mkl-service" />
            <item index="276" class="java.lang.String" itemvalue="async-timeout" />
            <item index="277" class="java.lang.String" itemvalue="pydeck" />
            <item index="278" class="java.lang.String" itemvalue="spacy-loggers" />
            <item index="279" class="java.lang.String" itemvalue="cloudpickle" />
            <item index="280" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="281" class="java.lang.String" itemvalue="preshed" />
            <item index="282" class="java.lang.String" itemvalue="requests-oauthlib" />
            <item index="283" class="java.lang.String" itemvalue="Cython" />
            <item index="284" class="java.lang.String" itemvalue="nbformat" />
            <item index="285" class="java.lang.String" itemvalue="tzdata" />
            <item index="286" class="java.lang.String" itemvalue="pipreqs" />
            <item index="287" class="java.lang.String" itemvalue="prometheus-client" />
            <item index="288" class="java.lang.String" itemvalue="tqdm" />
            <item index="289" class="java.lang.String" itemvalue="typing-inspect" />
            <item index="290" class="java.lang.String" itemvalue="grpcio" />
            <item index="291" class="java.lang.String" itemvalue="decord" />
            <item index="292" class="java.lang.String" itemvalue="pycocotools" />
            <item index="293" class="java.lang.String" itemvalue="google-auth" />
            <item index="294" class="java.lang.String" itemvalue="numpy" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>